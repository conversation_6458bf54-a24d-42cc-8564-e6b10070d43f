import sys

sys.path.append(r'D:\\CST Studio Suite 2022\\AMD64\\python_cst_libraries')
path = r'D:\\simulations\\model_0709.cst'

import cst.results

# 用于读取结果的项目对象（允许交互模式）
proj = cst.results.ProjectFile(path, allow_interactive=True)

# 尝试导入interface模块进行自动化控制

import cst.interface

# 使用CST接口连接到项目
de = cst.interface.DesignEnvironment()
# 直接使用DesignEnvironment创建项目连接
mws_proj = de.new_mws()
auto_control = True
print("成功连接到CST项目，支持自动化参数控制")


def get_parameter_list():
    """获取CST项目参数列表"""
    print("=== CST项目参数列表 ===")
    # 使用proj获取参数（从结果文件中读取）
    project_3d = proj.get_3d()
    param_combination = project_3d.get_parameter_combination(0)
    if param_combination:
        print(f"找到 {len(param_combination)} 个参数:")
        for name, value in param_combination.items():
            print(f"  {name}: {value}")
        return param_combination
    return {}

def adjust_parameter(param_name, delta_value):
    """增量调整单个参数值"""
    if auto_control:
        # 先获取当前参数值
        project_3d = proj.get_3d()
        current_params = project_3d.get_parameter_combination(0)
        current_value = current_params.get(param_name, 0)
        new_value = current_value + delta_value

        print(f"增量调整参数 {param_name}: {current_value} + {delta_value} = {new_value}")

        # 使用CST的VBA命令设置参数
        vba_code = f'''
Sub Main
    StoreParameter("{param_name}", {new_value})
End Sub
'''
        mws_proj.schematic.execute_vba_code(vba_code)
        print(f"参数 {param_name} 已调整为 {new_value}")
    else:
        print(f"增量调整参数 {param_name} + {delta_value}")
        print("注意：参数修改需要在CST Studio Suite界面中手动操作")

def set_parameter(param_name, param_value):
    """设置单个参数为指定值（绝对值设置）"""
    print(f"设置参数 {param_name} = {param_value}")
    if auto_control:
        # 使用CST的VBA命令设置参数
        vba_code = f'''
Sub Main
    StoreParameter("{param_name}", {param_value})
End Sub
'''
        mws_proj.schematic.execute_vba_code(vba_code)
        print(f"参数 {param_name} 已设置为 {param_value}")
    else:
        print("注意：参数修改需要在CST Studio Suite界面中手动操作")
        print(f"请在CST中打开参数管理器，将 {param_name} 设置为 {param_value}")

def adjust_multiple_parameters(param_deltas):
    """批量增量调整多个参数值"""
    print("=== 批量增量调整参数 ===")
    if auto_control:
        # 先获取当前所有参数值
        project_3d = proj.get_3d()
        current_params = project_3d.get_parameter_combination(0)

        # 生成VBA代码
        vba_lines = ["Sub Main"]
        for param_name, delta_value in param_deltas.items():
            current_value = current_params.get(param_name, 0)
            new_value = current_value + delta_value
            print(f"增量调整参数 {param_name}: {current_value} + {delta_value} = {new_value}")
            vba_lines.append(f'    StoreParameter("{param_name}", {new_value})')
        vba_lines.append("End Sub")
        vba_code = "\n".join(vba_lines)

        mws_proj.schematic.execute_vba_code(vba_code)
        print("所有参数增量调整完成")
    else:
        print("注意：参数修改需要在CST Studio Suite界面中手动操作")
        print("请在CST中打开参数管理器，增量调整以下参数:")
        for param_name, delta_value in param_deltas.items():
            print(f"  {param_name} += {delta_value}")

def set_multiple_parameters(param_dict):
    """批量设置多个参数为指定值（绝对值设置）"""
    print("=== 批量设置参数 ===")
    if auto_control:
        # 自动化模式 - 生成完整的VBA代码
        vba_lines = ["Sub Main"]
        for param_name, param_value in param_dict.items():
            print(f"设置参数 {param_name} = {param_value}")
            vba_lines.append(f'    StoreParameter("{param_name}", {param_value})')
        vba_lines.append("End Sub")
        vba_code = "\n".join(vba_lines)

        mws_proj.schematic.execute_vba_code(vba_code)
        print("所有参数已设置完成")
    else:
        # 手动模式
        print("注意：参数修改需要在CST Studio Suite界面中手动操作")
        print("请在CST中打开参数管理器，设置以下参数:")
        for param_name, param_value in param_dict.items():
            print(f"  {param_name} = {param_value}")

def set_mesh_fixed():
    """设置网格为固定模式"""
    print("设置网格为固定模式...")
    if auto_control:
        vba_code = '''
Sub Main
    ' 设置网格为固定模式
    Mesh.MeshType "PBA"
    Mesh.SetCreator "High Frequency"
End Sub
'''
        mws_proj.schematic.execute_vba_code(vba_code)
        print("网格已设置为固定模式")
    else:
        print("请在CST中手动设置网格为固定模式")

def rebuild_model():
    """重建模型（保持网格固定）"""
    print("重建模型...")
    if auto_control:
        # 使用VBA命令重建，但保持网格设置
        vba_code = '''
Sub Main
    ' 重建模型但保持网格设置
    Rebuild
End Sub
'''
        mws_proj.schematic.execute_vba_code(vba_code)
        print("模型已重建完成（网格保持固定）")
    else:
        # 手动模式
        print("请在CST Studio Suite中按 Ctrl+B 或点击重建按钮")

def run_simulation():
    """运行仿真（使用固定网格）"""
    print("开始运行仿真（使用固定网格）...")
    if auto_control:
        # 启动求解器
        vba_code = '''
Sub Main
    ' 启动求解器
    Solver.Start
End Sub
'''
        mws_proj.schematic.execute_vba_code(vba_code)
        print("仿真已启动（固定网格模式）")
    else:
        # 手动模式
        print("请在CST Studio Suite中点击求解器运行按钮")

def run_full_simulation():
    """运行完整仿真流程（固定网格模式）"""
    print("=== 运行完整仿真流程（固定网格模式）===")
    # 设置网格为固定模式
    set_mesh_fixed()
    # 重建模型
    rebuild_model()
    # 运行仿真
    run_simulation()
    print("完整仿真流程已启动（固定网格模式）")

# 使用示例
if __name__ == "__main__":
    print("=== CST参数控制和仿真示例 ===")

    print("\n=== 可用函数说明 ===")
    print("1. get_parameter_list() - 获取所有参数")
    print("2. adjust_parameter(name, delta) - 增量调整单个参数")
    print("3. adjust_multiple_parameters(dict) - 批量增量调整参数")
    print("4. set_parameter(name, value) - 设置单个参数为指定值")
    print("5. set_multiple_parameters(dict) - 批量设置参数为指定值")
    print("6. set_mesh_fixed() - 设置网格为固定模式")
    print("7. rebuild_model() - 重建模型")
    print("8. run_simulation() - 运行仿真")
    print("9. run_full_simulation() - 完整仿真流程（固定网格）")

    print("\n=== 使用示例 ===")
    print("# 获取参数列表")
    print("params = get_parameter_list()")
    print()
    print("# 增量调整单个参数（推荐）")
    print('adjust_parameter("dx", 0.2)  # dx增加0.2')
    print()
    print("# 批量增量调整参数（推荐）")
    print("param_deltas = {'dx': 0.2, 'hsub': 0.1}")
    print("adjust_multiple_parameters(param_deltas)")
    print()
    print("# 设置参数为绝对值")
    print('set_parameter("dx", 3.5)')
    print()
    print("# 运行完整仿真（固定网格）")
    print("run_full_simulation()")

    print("\n=== 实际执行示例 ===")
    # 增量调整参数示例
    param_deltas = {'dx': 0.2, 'hsub': 0.1}
    adjust_multiple_parameters(param_deltas)
    run_full_simulation()
    
# 原有的S参数获取功能（保留作为参考）
"""
s11 = proj.get_3d().get_result_item(r'1D Results\S-Parameters\S1,1')
print("S11参数信息:")
print(f"X轴标签: {s11.xlabel}")
print(f"Y轴标签: {s11.ylabel}")
print(f"X轴数据: {s11.get_xdata()}")
"""